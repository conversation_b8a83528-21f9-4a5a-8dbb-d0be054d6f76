<template>
  <div class="codeScanDialog">
    <xyy-dialog title="追溯码录入单" ref="scanDialog" width="948px">
      <el-form ref="formData" :model="formData" label-width="100px" class="clearfix">
        <el-col :lg="7" :md="7">
          <el-form-item label="单据编号" prop="refundCode">
            <el-input v-model="formData.refundCode" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="退货数" prop="actualRefundCount">
            <el-input v-model="formData.actualRefundCount" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="8">
          <el-form-item label="供应商" prop="supplierNameScan">
            <el-input v-model="formData.supplierNameScan" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :lg="3" :md="3">
          <el-button type="warning" icon="el-icon-open" style="float: right;" v-if="getPermission(unlockCode)"
            @click="unlock">解锁</el-button>
        </el-col>
      </el-form>
      <vxe-table ref="xTable" highlight-current-row highlight-hover-row height="120" :data="tableData">
        <vxe-table-column field="productCode" title="商品编码" min-width="135px" />
        <vxe-table-column field="specifications" title="规格" min-width="135px" />
        <vxe-table-column field="productName" title="名称" min-width="135px" />
        <vxe-table-column field="manufacturer" title="生产厂家" min-width="135px" />
        <vxe-table-column field="packingUnit" title="包装单位" min-width="135px" />
        <vxe-table-column field="middlePackingNumber" title="中包装规格" min-width="135px" />
        <vxe-table-column field="largePackingNumber" title="件包装规格" min-width="135px" />
        <vxe-table-column field="superviseTypeName  " title="监管类型 " min-width="135px" />
      </vxe-table>
      <vxe-table ref="yTable" highlight-current-row highlight-hover-row height="120" :data="tableData">
        <vxe-table-column field="waitLargePackingNumber" title="待扫描件包装追溯码数" min-width="135px" />
        <vxe-table-column field="scannedNumberLarge" title="已扫描件包装追溯码数" min-width="135px" />
        <vxe-table-column field="scannedNumberMiddle" title="已扫描中包装追溯码数" min-width="135px" />
        <vxe-table-column field="waitSmallPackingNumber" title="待扫描小包装追溯码数" min-width="135px" />
        <vxe-table-column field="scannedNumberSmall" title="已扫描小包装追溯码数" min-width="135px" />
        <vxe-table-column field="okSmallPackingNumber" title="已扫描数" min-width="135px" />
      </vxe-table>
      <vxe-table ref="zTable" highlight-current-row highlight-hover-row :data="detailTableData"
        @cell-click="currentChangeEvent" height="160">
        <vxe-table-column field="regulatoryCode" title="追溯码" />
        <vxe-table-column field="packageLevel" title="包装规格">
          <template slot-scope="scope">
            <span v-if="scope.row.packageLevel == 1">小包装</span>
            <span v-else-if="scope.row.packageLevel == 2">中包装</span>
            <span v-else-if="scope.row.packageLevel == 3">件包装</span>
          </template>
        </vxe-table-column>
        <vxe-table-column field="pkgAmount" title="小包装数量" />
        <vxe-table-column field="caozuo" title="操作">
          <template slot-scope="scope">
            <el-button @click="deleteMess(scope)" icon="el-icon-delete" type="danger"
              v-if="getPermission(deleteCode)">删除</el-button>
          </template>
        </vxe-table-column>
      </vxe-table>
      <el-form ref="bottomFormData" :model="bottomFormData" class="clearfix" style="transform: translateY(40%);"
        label-width="120px">
        <el-col :lg="6" :md="6">
          <el-form-item label="包装规格" prop="specificationSub">
            <el-select v-model="bottomFormData.specificationSub">
              <el-option label="小包装" value="1" />
              <el-option label="中包装" value="2" />
              <el-option label="件包装" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item prop="pda" label-width="50px">
            <el-input v-model="bottomFormData.pda" placeholder="扫描或手动输入" @input="checkPda"
              @keydown.enter.native="codeScanSubmit"></el-input>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="状态" label-width="70px">
            <h4>{{ codeStatus }}</h4>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-button type="primary" icon="el-icon-search" style="float: right;" v-if="getPermission(handleSearchCode)"
            @click="handleSearch">手动查询</el-button>
        </el-col>
      </el-form>
    </xyy-dialog>
    <un-lock-dialog ref="loginDialog" @on-close="loginDialogClose" />
  </div>
</template>

<script>
import { buyExitRreviewSubmit } from "@/api/buyExit/exitPreview.js"
import { unlockThisTask } from '@/api/buyExit/exitCodeScan.js'
import { buyExitGetNoScanList, exitCodeScanSubmit, exitCodeScanSubmitDetail, exitCodeScanDelete } from '@/api/buyExit/exitCodeScan.js'
import unLockDialog from "./unLockDialog.vue";
export default {
  name: 'codeScanDialog',
  components: { unLockDialog },
  data() {
    return {
      formData: {
        refundCode: '',
        actualRefundCount: '',
        supplierNameScan: ''
      },
      tableData: [],
      detailTableData: [],
      tableDataList: '',
      bottomFormData: {
        specificationSub: '1',
        pda: ''
      },
      pickUpOrder: '',
      codeStatus: '',
      // 删除时选中行的数据
      detailSelectData: '',
      // 按钮权限
      unlockCode: 'btn:wms:exitReview:unlock',
      deleteCode: 'btn:wms:exitReview:delete',
      handleSearchCode: 'btn:wms:exitReview:handleSearchCode',
    }
  },
  methods: {
    getPermission(code) {
      if (!this.$route.meta.buttonList) {
        return false;
      }
      const permissions = this.$route.meta.buttonList.map((item) => {
        return item.code;
      });
      return permissions.indexOf(code) !== -1;
    },
    open(selectData) {
      this.pickUpOrder = selectData.pickUpOrder
      this.selectData = selectData
      this.getNoScanDetailData()
      this.$refs.scanDialog.open()
    },
    close() {
      this.bottomFormData = {
        specificationSub: '1',
        pda: ''
      }
      this.codeStatus = ''
      this.$emit("refresh")
      this.$refs.scanDialog.close()
    },
    currentChangeEvent(val) {
      this.detailSelectData = val.row
    },
    // 删除按钮
    deleteMess(scope) {
      exitCodeScanDelete({ ids: scope.row.id }).then(res => {
        const { code, msg } = res
        if (code === 0) {
          this.getNoScanDetailData()
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 校验输入是否合规
    checkPda() {
      // 只允许输入数字
      this.bottomFormData.pda = this.bottomFormData.pda.replace(/\D/g, '')
      // console.log(this.tableDataList, 'this.tableDataList.largeCategory');
      
      if (this.tableDataList.largeCategory != 202 && this.tableDataList.largeCategory != '医疗器械') {
        if (this.bottomFormData.pda.length > 20) {
          this.bottomFormData.pda = this.bottomFormData.pda.slice(0, 20)
        }
        // 必须以 8 开头
        if (this.bottomFormData.pda.length === 1 && this.bottomFormData.pda[0] !== '8') {
          this.bottomFormData.pda = ''
          return
        }
      }
    },
    // 查询未扫描明细
    getNoScanDetailData() {
      buyExitGetNoScanList({ pickUpOrder: this.pickUpOrder }).then(res => {
        const { code, msg, result } = res
        if (code === 0) {
          if (result == null) {
            this.close()
          } else {
            this.tableData = [result]
            this.tableDataList = result
            this.selectData.lineNumber = result.lineNumber
            this.formData.refundCode = this.pickUpOrder
            this.formData.actualRefundCount = result.actualRefundCount
            this.formData.supplierNameScan = result.supplierName
            this.codeScanSubmitDetail()
          }
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 追溯码扫描提交
    codeScanSubmit() {
      if (this.bottomFormData.pda === '') {
        this.$message.warning('请输入追溯码')
        return
      }
      if(this.tableDataList.largeCategory != 202 && this.tableDataList.largeCategory != '医疗器械'){
        if (this.bottomFormData.pda.length < 20) {
          this.$message.warning('请正确输入追溯码')
          return
        }
        // 必须以 8 开头
        if (this.bottomFormData.pda.length === 1 && this.bottomFormData.pda[0] !== '8') {
          this.$message.warning('请正确输入追溯码')
          return
        }
      }
      const params = Object.assign({
        regulatoryCode: this.bottomFormData.pda,
        productCode: this.tableDataList.productCode,
        refundOrderCode: this.selectData.refundOrderCode,
        lineNumber: this.tableDataList.lineNumber,
        actualRefundCount: this.tableDataList.actualRefundCount,
        pickUpOrder: this.tableDataList.pickUpOrder,
        codeLevel: this.bottomFormData.specificationSub
      })
      exitCodeScanSubmit(params).then(res => {
        const { code, msg, result } = res
        if (code === 0) {
          this.detailTableData = [result]
          this.bottomFormData.pda = ''
          // 再次调用获取追溯码信息
          this.getNoScanDetailData()
          this.codeStatus = '已记录'
        } else {
          this.$message.error(msg)
          this.bottomFormData.pda = ''
          this.codeStatus = '未记录'
        }
      })
    },
    // 追溯码扫描详情
    codeScanSubmitDetail() {
      console.log("lineNumber ", this.selectData)
      const params = Object.assign({
        orderCode: this.selectData.pickUpOrder,
        lineNumber: this.selectData.lineNumber
      })
      exitCodeScanSubmitDetail(params).then(res => {
        const { result, msg, code } = res
        if (code === 0) {
          this.detailTableData = result
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 手动查询
    handleSearch() {
      this.codeScanSubmit()
    },
    unlock() {
      this.$refs.loginDialog.open()
    },
    loginDialogClose(userInfo) {
      const params = {
        pickUpOrder: this.pickUpOrder,
        lineNumber: this.tableDataList.lineNumber,
        unlockUser: userInfo.oaId
      }
      unlockThisTask(params).then(res => {
        const { result, msg, code } = res
        if (code === 0) {
          if (!result) {
            if (result == null) {
              buyExitRreviewSubmit({ pickUpOrder: this.pickUpOrder }).then(res => {
                const { code, msg } = res
                if (code === 0) {
                  this.$message.success(msg)
                } else {
                  this.$message.error(msg)
                }
              })
            }
            //关闭弹窗
            this.close()
          } else {
            this.tableData = [result]
            this.tableDataList.lineNumber = result.lineNumber
            this.exitCodeScanSubmitDetail()
          }
        } else {
          this.$message.error(msg)
        }
      })
    }
  },

}
</script>

<style lang="scss" scoped>
.table-box {
  height: 100px !important;
}
</style>