import request from "@/utils/request"

//销售订单情况监控查询
export function getStockoutOrderMonitor(data) {
  return request({
    url: "/outstock/web/outstock/salesorder/monitorstats/statsSalesOrder",
    method: "post",
    data
  })
}

//拣货过程监控查询
export function getStockoutPickMonitor(data) {
  return request({
    url: "/outstock/web/outstock/salesorder/monitorstats/statsPicking",
    method: "post",
    data
  })
}

//内复核监控查询
export function getStockoutInnerReviewMonitor(data) {
  return request({
    url: "/outstock/web/outstock/salesorder/monitorstats/statsInReview",
    method: "post",
    data
  })
}

//作业质量监控查询
export function getStockoutWorkQualityMonitor(data) {
  return request({
    url: "/outstock/web/outstock/salesorder/monitorstats/statsWorkQuality",
    method: "post",
    data
  })
}

//拆零产能监控查询
export function getStockoutSplitCapacityMonitor(data) {
  return request({
    url: "/outstock/web/outstock/salesorder/monitorstats/statsParts",
    method: "post",
    data
  })
}

//作业进度监控
export function getSjobProgressMonitoring(data) {
  return request({
    url: "/outstock/web/outstock/salesorder/monitorstats/jobProgressMonitoring",
    method: "post",
    data
  })
}