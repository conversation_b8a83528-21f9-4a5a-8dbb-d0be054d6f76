<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端聊天界面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }

        .mobile-chat-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #f5f5f5;
            max-width: 375px;
            margin: 0 auto;
            border: 1px solid #ddd;
        }

        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: #fff;
            border-bottom: 1px solid #e5e5e5;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .back-icon {
            font-size: 20px;
            color: #333;
            margin-right: 12px;
            cursor: pointer;
        }

        .store-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            line-height: 1.2;
        }

        .store-status {
            font-size: 12px;
            color: #999;
            margin-top: 2px;
        }

        .end-chat-btn {
            font-size: 14px;
            color: #666;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .welcome-section {
            text-align: center;
            padding: 16px;
            background-color: #f5f5f5;
        }

        .welcome-text {
            font-size: 14px;
            color: #999;
            background-color: #e5e5e5;
            padding: 6px 12px;
            border-radius: 12px;
            display: inline-block;
        }

        .session-divider {
            text-align: center;
            padding: 12px 0;
            margin: 8px 0;
        }

        .divider-text {
            font-size: 12px;
            color: #999;
            background-color: #e5e5e5;
            padding: 4px 12px;
            border-radius: 10px;
            display: inline-block;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 0 16px;
            background-color: #f5f5f5;
        }

        .message-item {
            display: flex;
            margin-bottom: 16px;
            align-items: flex-start;
        }

        .message-right {
            flex-direction: row-reverse;
        }

        .message-right .message-content {
            align-items: flex-end;
            margin-right: 8px;
        }

        .message-right .message-info {
            text-align: right;
        }

        .message-left .message-content {
            margin-left: 8px;
        }

        .avatar-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 500;
            flex-shrink: 0;
        }

        .message-content {
            display: flex;
            flex-direction: column;
            max-width: 70%;
        }

        .message-info {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-size: 12px;
            color: #999;
        }

        .sender-name {
            margin-right: 8px;
        }

        .message-bubble {
            padding: 10px 14px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .service-bubble {
            background-color: #fff;
            color: #333;
            border: 1px solid #e5e5e5;
        }

        .user-bubble {
            background-color: #95ec69;
            color: #333;
        }

        .timeout-tip {
            text-align: center;
            padding: 12px;
            font-size: 14px;
            color: #999;
            background-color: #f5f5f5;
        }

        .chat-input-area {
            padding: 12px 16px;
            background-color: #fff;
            border-top: 1px solid #e5e5e5;
        }

        .input-container {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 20px;
            padding: 8px 12px;
        }

        .message-input {
            flex: 1;
            border: none;
            background: transparent;
            outline: none;
            font-size: 14px;
            padding: 0;
        }

        .send-btn {
            width: 28px;
            height: 28px;
            border: none;
            background-color: #4183d5;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin-left: 8px;
        }

        .start-chat-container {
            padding: 16px;
            background-color: #fff;
        }

        .start-chat-btn {
            width: 100%;
            padding: 12px;
            background-color: #22c55e;
            color: white;
            border: none;
            border-radius: 24px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="mobile-chat-container">
        <!-- 顶部导航栏 -->
        <div class="chat-header">
            <div class="header-left">
                <div class="back-icon">←</div>
                <div class="store-info">
                    <div class="store-name">藏龙岛鼎兴药店</div>
                    <div class="store-status">待回复（自己）</div>
                </div>
            </div>
            <div class="header-right">
                <span class="end-chat-btn">结束会话</span>
            </div>
        </div>

        <!-- 欢迎访问标识 -->
        <div class="welcome-section">
            <span class="welcome-text">欢迎来访</span>
        </div>

        <!-- 聊天消息列表 -->
        <div class="chat-messages">
            <!-- 第一条消息 -->
            <div class="message-item message-left">
                <div class="message-avatar">
                    <div class="avatar-circle" style="background-color: #4183d5;">药</div>
                </div>
                <div class="message-content">
                    <div class="message-info">
                        <span class="sender-name">藏龙岛鼎兴药店</span>
                        <span class="message-time">2024/10/11 16:01:52</span>
                    </div>
                    <div class="message-bubble service-bubble">
                        您已确诊过此疾病并使用过该药，且无过敏史，无相关禁忌症和不良反应。请问您是否还有信息需要补充？如无，将依据病情为您开处方
                    </div>
                </div>
            </div>

            <!-- 接入会话分隔线 -->
            <div class="session-divider">
                <span class="divider-text">接入会话</span>
            </div>

            <!-- 第二条消息 -->
            <div class="message-item message-left">
                <div class="message-avatar">
                    <div class="avatar-circle" style="background-color: #4183d5;">药</div>
                </div>
                <div class="message-content">
                    <div class="message-info">
                        <span class="sender-name">某某客服</span>
                        <span class="message-time">2024/10/11 16:02:52</span>
                    </div>
                    <div class="message-bubble service-bubble">
                        您好，我是某某客服，很高兴为您服务
                    </div>
                </div>
            </div>

            <!-- 第三条消息 -->
            <div class="message-item message-left">
                <div class="message-avatar">
                    <div class="avatar-circle" style="background-color: #4183d5;">药</div>
                </div>
                <div class="message-content">
                    <div class="message-info">
                        <span class="sender-name">某某客服</span>
                        <span class="message-time">2024/10/11 16:03:12</span>
                    </div>
                    <div class="message-bubble service-bubble">
                        温馨提示：百亿补贴活动期间订单充值量较大，充值时效会受到影响，建议下单后2小时再查询到账情况；订单显示交易成功则代表购买成功
                    </div>
                </div>
            </div>

            <!-- 第四条消息（用户发送） -->
            <div class="message-item message-right">
                <div class="message-avatar">
                    <div class="avatar-circle" style="background-color: #22c55e;">客</div>
                </div>
                <div class="message-content">
                    <div class="message-info">
                        <span class="sender-name">藏龙岛鼎兴药店</span>
                        <span class="message-time">2024/10/11 16:40:13</span>
                    </div>
                    <div class="message-bubble user-bubble">
                        我不想要了，现在申请退货可以吗？
                    </div>
                </div>
            </div>
        </div>

        <!-- 客服超时结束会话提示 -->
        <div class="timeout-tip">
            客服超时结束会话
        </div>

        <!-- 底部输入区域 -->
        <div class="chat-input-area">
            <div class="input-container">
                <input type="text" placeholder="请输入" class="message-input" />
                <button class="send-btn">+</button>
            </div>
        </div>

        <!-- 发起对话按钮 -->
        <div class="start-chat-container">
            <button class="start-chat-btn">发起对话</button>
        </div>
    </div>
</body>
</html>
