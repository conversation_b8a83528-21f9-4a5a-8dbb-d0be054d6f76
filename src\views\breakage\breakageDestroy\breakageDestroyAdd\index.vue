<template>
    <div class="app-container">
        <xyy-panel title="新增不合格品销毁单">
            <!-- 按钮组 end-->
            <btn-group slot="tools" :btn-list="btnList" style="margin-bottom: 5px;" />
            <!-- 查询条件 start -->
            <el-form ref="formData" :model="formData" label-width="120px">
                <el-row :gutter="20">
                    <el-col :lg="8" :md="8">
                        <el-form-item label="单据日期">
                            <el-date-picker v-model="formData.overflowingTime" type="date" :disabled="true" />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="8" :md="8">
                        <el-form-item label="销毁单号">
                            <el-input v-model="formData.destroyNo" :disabled="true" />
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :lg="6" :md="6">
                        <el-form-item label="制单人">
                            <el-input v-model="formData.createUser" :disabled="true" />
                        </el-form-item>
                    </el-col> -->
                    <!-- <el-col :lg="6" :md="6">
                        <el-form-item label="仓库名称">
                            <el-input v-model="formData.warehouseName" :disabled="true" />
                        </el-form-item>
                    </el-col> -->
                    <el-col :lg="8" :md="8">
                        <el-form-item label="业主">
                            <el-select v-model="formData.ownerCode" @change="getOwnerName" clearable>
                                <el-option v-for="item in ownerCodeOptions" :key="item.dictCode" :value="item.dictCode"
                                    :label="item.dictName" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <!-- <el-col :lg="6" :md="6">
                        <el-form-item label="建筑物">
                            <el-select v-model="formData.buildingCode" clearable>
                                <el-option label="全部" value="" />
                                <el-option v-for="item in buildingCode" :key="item.dictCode" :value="item.dictCode"
                                    :label="item.dictName" />
                            </el-select>
                        </el-form-item>
                    </el-col> -->
                    <el-col :lg="8" :md="8">
                        <el-form-item label="销毁方式">
                            <el-input v-model="formData.destroyWayTxt" />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="8" :md="8">
                        <el-form-item label="销毁地点">
                            <el-input v-model="formData.destroyAddrTxt" />
                        </el-form-item>
                    </el-col>
                    <el-col :lg="8" :md="8">
                        <el-form-item label="销毁单位">
                            <el-input v-model="formData.signatureDrugAdminTxt" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :lg="8" :md="8">
                        <el-form-item label="销毁监督人">
                            <el-input v-model="formData.signatureWatcherTxt" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </xyy-panel>
        <xyy-panel title="商品明细">
            <btn-group slot="tools" :btn-list="btns" style="margin-bottom: 5px;" />
            <div v-table class="table-box">
                <vxe-table ref="xTable" :loading="loading" highlight-current-row highlight-hover-row height="auto"
                    :data="tableData" :seq-config="{ startIndex: (tablePage.pageNo - 1) * tablePage.pageSize }"
                    :row-class-name="rowClassName" @current-change="currentChangeEvent">
                    <!-- <vxe-column type="checkbox" width="60"></vxe-column> -->
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <template v-for="item in tableColumns">
                        <vxe-table-column v-if="item.visible" :key="item.field" :field="item.field" :title="item.title"
                            :min-width="item.width">
                            <!-- 使用插槽 -->
                            <template #default="{ row }">
                                <span v-if="item.field === 'destroyWay'">
                                    <el-input v-model="row.destroyWay"></el-input>
                                </span>
                                <span v-else-if="item.field === 'destroyAddr'">
                                    <el-input v-model="row.destroyAddr"></el-input>
                                </span>
                                <span v-else-if="item.field === 'signatureDrugAdmin'">
                                    <el-input v-model="row.signatureDrugAdmin"></el-input>
                                </span>
                                <span v-else-if="item.field === 'signatureWatcher'">
                                    <el-input v-model="row.signatureWatcher"></el-input>
                                </span>
                                <span v-else>{{ row[item.field] }}</span>
                            </template>
                        </vxe-table-column>
                    </template>
                </vxe-table>
            </div>
            <!-- <div class="pager">
                <vxe-pager border :current-page="tablePage.pageNo" :page-size="tablePage.pageSize"
                    :total="tablePage.total" :page-sizes="tablePage.pageSizes" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
            </div> -->
        </xyy-panel>
        <div>
            <extract-dialog ref="extractDialog" @on-close="extractDialogClose" :formInfo="formData"
                @confirm="extractConfirm" />
        </div>
    </div>
</template>

<script>
import {
    getDictCode,
    getDestroyNo,
    addDestroy,
} from '@/api/breakage/breakageDestroyAdd.js'
import { tableColumns } from './config';
import extractDialog from './components/extractDialog.vue';
import { deepClone } from '@/utils/index.js'
import XEUtils, { isArguments } from 'xe-utils'
import { formatDateRange } from "@/utils/index.js"

const defaultDate = new Date()
const defaultDateFormat = XEUtils.toDateString(defaultDate, 'yyyy-MM-dd');
export default {
    name: 'BreakageDestroyAdd',
    components: {
        extractDialog
    },
    data() {
        return {
            btnList: [
                {
                    label: '返回',
                    type: 'info',
                    icon: 'el-icon-back',
                    clickEvent: this.backHandle,
                    code: 'btn:wms:breakageDestroyAdd:back'
                },
                {
                    label: '保存',
                    type: 'success',
                    icon: 'el-icon-check',
                    loading: this.saveLoading,
                    clickEvent: this.saveHandle,
                    code: 'btn:wms:breakageDestroyAdd:save'
                },
                {
                    label: '提取',
                    type: 'primary',
                    icon: 'el-icon-search',
                    clickEvent: this.extractHande,
                    code: 'btn:wms:breakageDestroyAdd:extract'
                },
            ],
            btns: [
                {
                    label: '批量更新',
                    type: 'success',
                    icon: 'el-icon-edit',
                    clickEvent: this.batchUpdate,
                    code: 'btn:wms:breakageDestroyAdd:batchUpdate'
                },
                {
                    label: '删除行',
                    type: 'danger',
                    icon: 'el-icon-delete',
                    clickEvent: this.deleteRow,
                    code: 'btn:wms:breakageDestroyAdd:deleteRow'
                },
            ],
            formData: {
                overflowingTime: defaultDateFormat,
                destroyNo: '',
                createUser: '',
                warehouseName: '',
                ownerCode: '',
                // buildingCode: '',
                destroyWayTxt: '',
                destroyAddrTxt: '',
                signatureDrugAdminTxt: '',
                ownerName: '',
                signatureWatcherTxt: ''
            },
            ownerCodeOptions: [],
            buildingCode: [],
            loading: false,
            tableData: [],
            tablePage: {
                pageNo: 1,
                pageSize: 10,
                total: 0,
                pageSizes: [
                    {
                        label: '10条/页',
                        value: 10
                    },
                    {
                        label: '20条/页',
                        value: 20
                    },
                    {
                        label: '50条/页',
                        value: 50
                    },
                    {
                        label: '100条/页',
                        value: 100
                    },
                    {
                        label: '500条/页',
                        value: 500
                    },
                    {
                        label: '1000条/页',
                        value: 1000
                    }
                ]
            },
            selectData: '',
            tableColumns: tableColumns(),
            saveLoading: false,
        }
    },
    // mounted() {
    //     // this.getBuildingCode()
    //     this.getOwnerCode()
    //     this.apiGetDestroyNo()
    // },

    activated() {
        this.getOwnerCode()
        this.apiGetDestroyNo()
    },
    methods: {
        resetHandler() {
        },
        handlePageChange({ currentPage, pageSize }) {
            this.tablePage.pageNo = currentPage
            this.tablePage.pageSize = pageSize
            this.searchHandle()
        },
        rowClassName(row, rowlndex, $rowlndex) {
            if (row.row.orderSort == 1) {
                return 'attach-lable';
            } else {
                return ''
            }
        },
        currentChangeEvent(val) {
            this.selectData = val
        },
        backHandle() {
            this.$confirm('返回后当前页面数据将丢失，是否继续', '温馨提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$store.state.tagsView.visitedViews = this.$store.state.tagsView.visitedViews.filter(
                    (item) => item.name !== "BreakageDestroyAdd"
                )
                this.$router.replace({ path: '/breakage/breakageDestroy/breakageDestroyList' })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消'
                })
            })
        },
        getBuildingCode() {
            const dictType = 'JZW'
            const params = Object.assign({ dictType: dictType })
            getDictCode(params).then(res => {
                const { code, msg, result } = res
                if (code === 0 && result) {
                    this.buildingCode = result
                }
            })
        },
        getOwnerCode() {
            const dictType = 'YZBM'
            const params = Object.assign({
                dictType: dictType
            })
            getDictCode(params).then(res => {
                const { code, msg, result } = res
                if (code === 0 && result) {
                    this.ownerCodeOptions = result
                }
            })
        },
        extractDialogClose(data) {

        },
        extractHande() {
            if (this.formData.ownerCode == '') {
                this.$message.warning('请选择业主')
                return
            }
            // if (this.formData.buildingCode == '') {
            //     this.$message.warning('请选择建筑物')
            //     return
            // }
            this.$refs.extractDialog.open()
        },
        apiGetDestroyNo() {
            getDestroyNo({}).then(res => {
                const { code, msg, result } = res
                if (code === 0 && result) {
                    this.formData.destroyNo = result
                }
            })
        },
        getOwnerName() {
            this.tableData = []
            this.formData.ownerName = this.ownerCodeOptions.filter(item => item.dictCode == this.formData.ownerCode)[0].dictName
        },
        extractConfirm(data) {
            // this.tableData.concat(data)
            if (this.tableData.length != 0) {
                for (let i = 0; i < data.length; i++) {
                    if (this.tableData.findIndex(item => item.id == data[i].id) != -1) {
                        this.$message.warning('不能重复提取报损单')
                        return
                    }
                }
            }
            data.forEach(item => {
                item.destroyDesc = '定期销毁'
                item.signatureExecutor = '储运部'
            })
            data.forEach(item => {
                this.tableData.push(item)
            })
        },
        batchUpdate() {
            if (this.formData.destroyWayTxt != '') {
                this.tableData.forEach(item => {
                    // item.destroyWay = this.formData.destroyWayTxt
                    this.$set(item, 'destroyWay', this.formData.destroyWayTxt)
                })
            }
            if (this.formData.destroyAddrTxt != '') {
                this.tableData.forEach(item => {
                    // item.destroyAddr = this.formData.destroyAddrTxt
                    this.$set(item, 'destroyAddr', this.formData.destroyAddrTxt)
                })
            }
            if (this.formData.signatureDrugAdminTxt != '') {
                this.tableData.forEach(item => {
                    // item.signatureDrugAdmin = this.formData.signatureDrugAdminTxt
                    this.$set(item, 'signatureDrugAdmin', this.formData.signatureDrugAdminTxt)
                })
            }
            if (this.formData.signatureWatcherTxt != '') {
                this.tableData.forEach(item => {
                    this.$set(item, 'signatureWatcher', this.formData.signatureWatcherTxt)
                })
            }
        },
        deleteRow() {
            if (this.selectData.row == '' || this.selectData.row == null) {
                this.$message.warning('没有选中任何行')
                return
            }
            const index = this.tableData.indexOf(this.selectData.row)
            this.tableData.splice(index, 1)
        },
        saveHandle() {
            for (let i = 0; i < this.tableData.length; i++) {
                if (this.tableData[i].destroyWay == '' || this.tableData[i].destroyWay == null) {
                    this.$message.warning('请输入销毁方式')
                    return
                }
                if (this.tableData[i].destroyAddr == '' || this.tableData[i].destroyAddr == null) {
                    this.$message.warning('请输入销毁地点')
                    return
                }
                if (this.tableData[i].signatureDrugAdmin == '' || this.tableData[i].signatureDrugAdmin == null) {
                    this.$message.warning('请输入销毁单位')
                    return
                }
                if (this.tableData[i].signatureWatcher == '' || this.tableData[i].signatureWatcher == null) {
                    this.$message.warning('请输入销毁人')
                    return
                }
            }
            this.saveLoading = true
            const addData = this.tableData.map(item => {
                return {
                    id: item.id,
                    // id: item.storageId,
                    destroyWay: item.destroyWay,
                    destroyAddr: item.destroyAddr,
                    signatureDrugAdmin: item.signatureDrugAdmin,
                    signatureWatcher: item.signatureWatcher
                }
            })
            const params = Object.assign({
                destroyNo: this.formData.destroyNo,
                ownerCode: this.formData.ownerCode,
                detailsList: addData
            })
            addDestroy(params).then(res => {
                const { code, msg, result } = res
                if (code === 0) {
                    this.saveLoading = false
                    this.$message.success(msg)
                    this.tableData = []
                    this.formData.ownerCode = ''
                    this.apiGetDestroyNo()
                } else {
                    this.saveLoading = false
                    this.$message.error(msg)
                }
            })
        }
    }
}
</script>