import Layout from '@/layout';

export default {
  path: '/basic',
  component: Layout,
  alwaysShow: true,
  meta: {
    title: '基础资料',
    icon: 'headjichuziliao',
    code: 'menu:wms:basicdata',
  },
  children: [
    {
      path: '/basic/master', //主数据档案
      component: () => import('@/views/components/shellView/index.vue'),
      name: 'master',
      alwaysShow: true,
      meta: {
        title: '主数据档案',
        icon: 'headshujudangan-',
        code: 'menu:wms:master', //menu:wms:master
      },
      children: [
        {
          path: '/basic/master/freightRuleNew/list', //商品报告
          component: () => import('@/views/basic/freightRuleNew/index.vue'),
          name: 'freightRuleNew',
          meta: {
            title: '商品药检报告',
            // icon: 'headyaojianbaogao',
            code: 'menu:wms:FreightRuleNew'
          },
        },
        {
          path: '/basic/master/productBatchNumber/list', //商品批号
          component: () => import('@/views/basic/productBatchNumber/index.vue'),
          name: 'productBatchNumber',
          meta: {
            title: '商品批号',
            // icon: 'headpihao',
            code: 'menu:wms:productBatchNumber'
          },
        },
        {
          path: '/basic/supplier', //供应商资料
          component: () => import('@/views/basic/supplier/index'),
          name: 'supplier',
          meta: {
            title: '供应商资料',
            // icon: 'headgongyingshangziliao',
            code: 'menu:wms:supplierBase',
          },
        },
        {
          path: '/basic/supplier/detail', //供应商详情
          component: () => import('@/views/basic/supplier/detail'),
          name: 'supplierDetail',
          hidden: true,
          meta: {
            title: '供应商详情',
            // icon: 'headgongyingshangziliao',
            code: 'menu:wms:supplierDetail',
          },
        },
        {
          path: '/basic/customer', //客户
          component: () => import('@/views/basic/customer/index-new'),
          name: 'customer',
          meta: {
            title: '客户资料',
            // icon: 'headkehuziliao',
            code: 'menu:wms:customerBase',
          },
        },
        {
          path: '/basic/customer/detail', //客户详情
          component: () => import('@/views/basic/customer/detail'),
          name: 'customerDetail',
          hidden: true,
          meta: {
            title: '客户详情',
            // icon: 'headkehuziliao',
            code: 'menu:wms:customerDetail',
          },
        },
        {
          path: '/basic/goodsinfo', //商品资料
          component: () => import('@/views/basic/goodsInfo/index'),
          name: 'goodsinfo',
          meta: {
            title: '商品资料',
            // icon: 'headshangpin',
            code: 'menu:wms:productBase',
          },
        },
        {
            path: '/basic/validateManage', //效期商品管理
            component: () => import('@/views/basic/validateManage/index'),
            name: 'validateManage',
            meta: {
              title: '效期商品管理',
              // icon: 'headshangpin',
              //code: 'menu:wms:validateManage',
              code:"menu:wms:validateManage"
            },
        },
        {
          path: '/basic/goodsinfo/edit', //商品-编辑
          component: () => import('@/views/basic/goodsInfo/edit'),
          name: 'goodsinfoEdit',
          hidden: true,
          meta: {
            title: '商品-编辑',
            // icon: 'headshangpin',
            code: 'menu:wms:goodsinfoEdit',
          },
        },
      ],
    },
    {
      path: '/basic/basicdata/goodsPositionDict', //货位-容器-档案
      component: () => import('@/views/components/shellView/index.vue'),
      name: 'goodsPositionDict',
      alwaysShow: true,
      meta: {
        title: '货位容器档案',
        icon: 'headdangan-wenjianjia',
        code: 'menu:wms:goodsPositionDict',
      },
      children: [
        // {
        //   path: '/basic/basicdata/storageType/toStorageType', //库别字典
        //   component: () => import('@/views/basic/basicdata/storageType'),
        //   name: 'storageType',
        //   meta: {
        //     title: '库别字典',
        //     icon: 'el-icon-monitor',
        //     code: 'test',
        //   }
        // },
        {
          path: '/basic/basicdata/storageRoomArea/toStorageRoomArea', //库房库区字典
          component: ()=>  import('@/views/basic/basicdata/storageRoomArea'),
          name: 'storageRoomArea',
          meta: {
            title: '库房库区字典',
            // icon: 'headkufangguanli-kufangpandian',
            code: 'menu:wms:storageRoomArea',
          }
        },
        {
          path: '/basic/basicdata/goodsPosition/toGoodsPosition', //货位字典
          component: () => import('@/views/basic/basicdata/goodsPosition'),
          name: 'goodsPosition',
          meta: {
            title: '货位字典',
            // icon: 'headhuowei',
            code: 'menu:wms:goodsPosition',
          }
        },
        {
          path: '/productLogicalregionRelation/toProductRelation', //货位与商品关系维护
          component: () => import('@/views/basic/basicdata/productRelation'),
          name: 'productRelation',
          meta: {
            title: '货位与商品关系维护',
            // icon: 'headzhihuihouqin',
            code: 'menu:wms:productRelation',
          }
        },
        {
          path: '/basic/basicdata/container/to/home', //容器字典
          component: () => import('@/views/basic/basicdata/conDictionaryHome'),
          name: 'conDictionaryHomeList',
          meta: {
            title: '容器字典',
            // icon: 'headrongqi',
            code: 'menu:wms:conDictionaryHome',
          },
        },
        {
          path: '/basic/basicdata/container/to/status', //容器状态
          component: () => import('@/views/basic/basicdata/conDictionaryStatus'),
          name: 'conDictionaryStatus',
          meta: {
            title: '容器状态',
            // icon: 'headbaozhuangrongqizhuangtai',
            code: 'menu:wms:conDictionaryStatus',
          },
        },
      ]
    },
    // {
    //   path: '/basic/basicdata/conDictionaryHome', //容器-档案
    //   component: () => import('@/views/components/shellView/index.vue'),
    //   name: 'conDictionaryHome',
    //   alwaysShow: true,
    //   meta: {
    //     title: '容器档案',
    //     icon: 'el-icon-monitor',
    //     code: 'menu:wms:conDictionary',
    //   },
    //   children: [
    //     {
    //       path: '/basic/basicdata/conDictionaryHome/list', //容器字典
    //       component: () => import('@/views/basic/basicdata/conDictionaryHome'),
    //       name: 'conDictionaryHomeList',
    //       meta: {
    //         title: '容器字典',
    //         icon: 'el-icon-monitor',
    //         code: 'menu:wms:conDictionaryHome',
    //       },
    //     },
    //     {
    //       path: '/basic/basicdata/conDictionaryStatus/list', //容器状态
    //       component: () => import('@/views/basic/basicdata/conDictionaryStatus'),
    //       name: 'conDictionaryStatus',
    //       meta: {
    //         title: '容器状态',
    //         icon: 'el-icon-monitor',
    //         code: 'menu:wms:conDictionaryStatus',
    //       },
    //     },
    //   ],
    // },
    {
      path: '/basic/basicdata/gift', //字典-档案
      component: () => import('@/views/components/shellView/index.vue'),
      name: 'basicdataList',
      alwaysShow: true,
      meta: {
        title: '字典档案',
        icon: 'headcunchu',
        code: 'menu:wms:dictView',
      },
      children: [
        {
          path: '/basic/basicdata/filedDatamainTenance/list', //字段资料维护
          component: () => import('@/views/basic/basicdata/filedDatamainTenance'),
          name: 'filedDatamainTenance',
          meta: {
            title: '字段资料维护',
            // icon: 'headsuidaoziliaoguanli',
            code: 'menu:wms:dictBase',
          },
        },
        {
          path: '/basic/basicdata/paramSetting/list', //参数设置
          component: () => import('@/views/basic/basicdata/paramSetting'),
          name: 'basicdataParamSetting',
          meta: {
            title: '参数设置',
            // icon: 'head_canshu_canshushezhi',
            code: 'menu:wms:dictParam'//'menu:wms:dictParam',
          },
        },
        {
          path: '/basic/basicdata/gift/list', //赠品规则配置
          component: () => import('@/views/basic/basicdata/newGift'),
          name: 'basicdataGift',
          meta: {
            title: '赠品规则配置',
            // icon: 'headcard-',
            code: 'menu:erpscm:menu:gift',
          },
        },
      ],
    },
  ],
};
