<template>
  <div class="execute">
    <xyy-panel title="购进退出单查询">
      <btn-group slot="tools" :btn-list="btnList" />
      <!-- 查询条件 start -->
      <el-form ref="formData" :model="formData" label-width="120px" class="clearfix">
        <el-row :gutter="20">
          <el-col :lg="6" :md="6">
            <el-form-item label="购进退出单编号" prop="refundOrderCode">
              <el-input v-model="formData.refundOrderCode"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="商品名称" prop="productName">
              <el-input v-model="formData.productName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="采购员" prop="purchaseUser">
              <el-input v-model="formData.purchaseUser"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="供应商名称" prop="supplierName">
              <el-input v-model="formData.supplierName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :lg="12" :md="12">
            <el-form-item label="单据生成日期">
              <el-date-picker v-model="completeTime" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="业主名称" prop="ownerCode">
              <el-select v-model="formData.ownerCode">
                <el-option label="全部" value=""></el-option>
                <el-option :label="item.ownerName" :value="item.ownerCode" v-for="item in listOwners"
                  :key="item.ownerCode" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </xyy-panel>
    <xyy-panel title="购进退出单列表">
      <!--筛选列组件-->
      <div slot="tools" style="float:right;">
        <el-button class="setField" type="primary" @click="setingTableDataHander(0)">
          <svg aria-hidden="true"
            style="width: 1em; height: 1em; position: relative; top: 2px; fill: currentColor; overflow: hidden;"
            viewBox="0 0 1024 1024">
            <!-- # + iconfont项目内symbol图标代码 -->
            <use xlink:href="#icon-headxitongguanli" />
          </svg>
        </el-button>
      </div>
      <div v-table class="table-box">
        <vxe-table ref="xTable" :loading="loading" highlight-current-row highlight-hover-row height="auto"
          :data="tableData" @cell-click="getDetailList" @current-change="currentChangeEvent"
          :seq-config="{ startIndex: (tablePage.pageNum - 1) * tablePage.pageSize }" resizable
          @resizable-change="resizableChange" :columns="tableColumns" :key="tableKey">
          <vxe-table-column type="seq" title="序号" width="80" />
          <template  v-for="item in tableColumns">
          <vxe-table-column v-if="item.visible" :key="item.title" :field="item.field" :title="item.title"
            :min-width="item.width">
          </vxe-table-column>
        </template>
        </vxe-table>
      </div>
      <div class="pager">
        <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize" :total="tablePage.total"
          :layouts="[
            'PrevPage',
            'JumpNumber',
            'NextPage',
            'FullJump',
            'Sizes',
            'Total',
          ]" @page-change="handlePageChange" />
      </div>
    </xyy-panel>
    <xyy-panel title="购进退出单明细列表">
      <!--筛选列组件-->
      <div slot="tools" style="float:right;">
        <el-button class="setField" type="primary" @click="setingTableDataHander(1)">
          <svg aria-hidden="true"
            style="width: 1em; height: 1em; position: relative; top: 2px; fill: currentColor; overflow: hidden;"
            viewBox="0 0 1024 1024">
            <!-- # + iconfont项目内symbol图标代码 -->
            <use xlink:href="#icon-headxitongguanli" />
          </svg>
        </el-button>
      </div>
      <btn-group slot="tools" :btn-list="detailBtnList" />
      <div v-table class="table-box">
        <vxe-table ref="xTable" :loading="detailLoading" highlight-current-row highlight-hover-row height="auto"
          :data="detailTableData" @current-change="detailCurrentChangeEvent" resizable
          @resizable-change="resizableChangeDetail" :columns="detailTableColums" :key="tableKey">
          <vxe-table-column type="seq" title="序号" width="80" />
          <template v-for="item in detailTableColums">
          <vxe-table-column v-if="item.visible" :key="item.title" :field="item.field" :title="item.title"
            :min-width="item.width">
            <template #default="{ row }">
              <span v-if="item.field === 'containerCode'">
                <el-input disabled v-model="row[item.field]">
                  <el-button slot="append" icon="el-icon-search" @click="openContainerNumber" />
                </el-input>
              </span>
              <span v-else-if="item.field === 'shelfLocationCode'">
                <el-input disabled v-model="row[item.field]">
                  <el-button slot="append" icon="el-icon-search" @click="openShelfLocationCode(row)" />
                </el-input>
              </span>
              <span v-else-if="item.field === 'whetherRegulatory'">
                <span v-if="row.whetherRegulatory == 0">否</span>
                <span v-else-if="row.whetherRegulatory == 1">是</span>
              </span>
              <span v-else>{{ row[item.field] }}</span>
            </template>
          </vxe-table-column>
        </template>
        </vxe-table>
      </div>
    </xyy-panel>
    <!--筛选列组件导入-->
    <filter-table-head ref="filterDialog" @confirm="setFilterTableHead" @reset="resetFilterTableHead" />
    <container-dialog ref="containerDialog" @on-close="closeContainer"></container-dialog>
    <drop-out-dialog ref="dropOutDialog" @on-close="closeDropOut"></drop-out-dialog>
    <address-dialog ref="addressDialog" @get-address="getAddress" @submitApi="submitApi"
      @on-close="closeAddress"></address-dialog>
  </div>
</template>

<script>
//导入保存和查询自定义列接口 
import { queryUserCustomColumn, saveUserCustomColumn } from '@/api/public.js'
import utils from '@/utils'
import containerDialog from './components/containerDialog.vue'
import dropOutDialog from './components/dropOutDialog.vue'
import addressDialog from './components/addressDialog.vue'
import { exitExecuteDetailData, exitExecutData, exitSubmitFinall } from '@/api/buyExit/exitExecute.js'
import { getListOwners } from '@/api/buyExit/searchFor.js'
import { tableColumns, detailTableColums } from './colums'
import XEUtils from 'xe-utils'
import { formatDateRange } from "@/utils/index.js"
const end = new Date()
const start = new Date(end.getFullYear(), end.getMonth(), 1)  // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, 'yyyy-MM-dd') // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, 'yyyy-MM-dd') // 将结束日期格式化为字符串
export default {
  name: "exitExecute",
  components: {
    containerDialog,
    dropOutDialog,
    addressDialog
  },
  data() {
    return {
      // 日期
      completeTime: [defaultBeginTime, defaultEndTime],
      btnList: [
        {
          label: '查询',
          type: 'primary',
          clickEvent: this.searchList,
          icon: 'el-icon-search',
          loading: false,
          code: 'btn:wms:exitExecute:search'
        },
        {
          label: '重置',
          type: 'info',
          clickEvent: this.reset,
          icon: 'el-icon-refresh-left',
          code: 'btn:wms:exitExecute:reset'
        },
      ],
      detailBtnList: [
        {
          label: '还原明细行',
          type: 'info',
          icon: 'el-icon-refresh-left',
          clickEvent: this.restore,
          loading: false,
          code: 'btn:wms:exitExecute:restore'
        },
        {
          label: '提交',
          type: 'primary',
          icon: 'el-icon-check',
          clickEvent: this.submitFinall,
          loading: false,
          code: 'btn:wms:exitExecute:submit'
        },
      ],
      formData: {
        ownerCode: 'XYYYXKJGS',
        supplierName: '',
        purchaseUser: '',
        refundOrderCode: '',
        productName: ''
      },
      // 时间限制
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      loading: false,
      detailLoading: false,
      tableColumns: tableColumns(),
      oldTableColumns: JSON.parse(JSON.stringify(tableColumns())),
      tableKey: Date.now(), //表格列刷新flag
      storeDone: true, //后端是否存在自定义列宽数据 true->存在 false->不存在
      detailTableColums: detailTableColums(),
      oldDetailTableColums: JSON.parse(JSON.stringify(detailTableColums())),
      tableData: [],
      detailTableData: [],
      oldDetailTableData: [],
      // 业主列表
      listOwners: [],
      selectData: '',
      detailSelsectData: '',
      tablePage: {
        pageNum: 1,
        pageSize: 100,
        total: 0
      },
      // 收货地址
      takeDeliveryAddress: ''
    }
  },
  methods: {
    // 设置表头筛选列-子组件回传
    setFilterTableHead({ type, fullColumns, tableNo }) {
      if (tableNo === 'xTable') {
        this.tableColumns = [...fullColumns]
        this.tableKey = Date.now()
        this.setColumnWidth('exitExecute', 'tableColumns')
        this.getColumWidth('exitExecute', 'tableColumns', 'xTable')
      }
      if (tableNo === 'detailTable') {
        this.detailTableColums = [...fullColumns]
        this.tableKey = Date.now()
        this.setColumnWidth('exitExecuteDetail', 'detailTableColums')
        this.getColumWidth('exitExecuteDetail', 'detailTableColums', 'detailTable')
      }
    },
    //设置筛选列
    setingTableDataHander(index) {
      if (index == 0) {
        // this.type = index;
        let columns = [];
        columns = JSON.parse(JSON.stringify((this.tableColumns)));
        this.$refs.filterDialog.open(columns, 1, true, 'xTable')
      }
      if (index === 1) {
        let columns = [];
        columns = JSON.parse(JSON.stringify((this.detailTableColums)));
        this.$refs.filterDialog.open(columns, 1, true, 'detailTable')
      }
    },
    //重置自定义列宽方法
    //每一个vxe-table单独实现
    resetFilterTableHead(tableNo) {
      if (tableNo === 'xTable') {
        this.oldTableColumns = JSON.parse(JSON.stringify((tableColumns())));
        this.tableColumns = [...this.oldTableColumns]
        this.tableKey = Date.now()
        this.setColumnWidth('exitExecute', 'oldTableColumns')
        this.getColumWidth('exitExecute', 'tableColumns', 'xTable')
      }
      if (tableNo === 'detailTable') {
        this.oldDetailTableColums = JSON.parse(JSON.stringify((detailTableColums())));
        this.detailTableColums = [...this.oldDetailTableColums]
        this.tableKey = Date.now()
        this.setColumnWidth('exitExecuteDetail', 'oldDetailTableColums')
        this.getColumWidth('exitExecuteDetail', 'detailTableColums', 'detailTable')
      }
    },
    resizableChangeDetail({ column }) {
      this.detailTableColums[this.detailTableColums.findIndex(item => item.title === column.title)].width = column.resizeWidth
    },
    //监测拖动列宽变化方法
    resizableChange({ column }) {
      this.tableColumns[this.tableColumns.findIndex(item => item.title === column.title)].width = column.resizeWidth
    },
    //保存/更新自定义列方法实现
    //page: 自定义列的唯一标识 多为该单页routerName 单页存在多个表格则在此做变化
    // column： 所需要保存的表格绑定的列数据
    setColumnWidth(page, column) {
      const columns = this[column]
      const params = {
        page: page,
        columns: columns
      }
      saveUserCustomColumn(params).then(res => {
        const { code, msg, result } = res
        if (code === 0) {
          this.$message.success(msg)
        } else {
          this.$message.error(msg)
        }
      })
    },

    //查询自定义列方法实现
    // page:保存时所给的列唯一标识 一般是该单页的routerName 单页存在多个表格则每个表格的标识不同
    // column:所需要渲染的表格绑定的列数据
    // table: 所需要渲染的表格的 'ref'
    getColumWidth(page, column, table) {
      const params = {
        page: page,
      }
      queryUserCustomColumn(params).then(res => {
        const { code, msg, result } = res
        if (code === 0 && result) {
          const columns = result
          //出参只含field和width的匹配
          columns.forEach(item => {
            //更改对应column的
            this[column][this[column].findIndex(d => d.field === item.field)].width = item.width
            //匹配后端所传列顺序
            const foundItem = this[column].find(d => d.field === item.field)
            if (foundItem) {
              this[column].push(foundItem)
              this[column].splice(this[column].indexOf(foundItem), 1)
            }
          })
          this.storeDone = true //查询到列表格数据标识
          this.tableKey = Date.now() //强制刷新表格列
          // 若返回数据格式存在 field,title,visible,width
          // this[column] = columns
          // this.tableKey = Date.now() 
          // this.storeDone = true
        } else {
          this.storeDone = false
        }
      })
    },

    // 重置列表
    reset() {
      this.formData = {
        ownerCode: 'XYYYXKJGS',
        supplierName: '',
        purchaseUser: '',
        refundOrderCode: '',
        productName: ''
      }
      this.completeTime = [defaultBeginTime, defaultEndTime]
    },
    // 还原明细行
    restore() {
      if (this.detailTableData.length) {
        this.detailTableData = JSON.parse(JSON.stringify(this.oldDetailTableData))
      }
    },
    // 选中行的数据
    currentChangeEvent(val) {
      this.selectData = val.row
    },
    // 获取字典
    getOwners() {
      const params = ''
      getListOwners(params).then(res => {
        const { result, code, msg } = res
        if (code === 0) {
          this.listOwners = result
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 初始列表查询
    getList() {
      this.loading = true
      this.btnList[this.btnList.findIndex(item => item.label === '查询')].loading = true
      const { pageNum, pageSize } = this.tablePage
      const [beginTime, endTime] = formatDateRange(this.completeTime)
      const params = Object.assign({ pageNum, pageSize }, { beginTime, endTime }, { orderType: 1, refundOrderStatus: 1 }, this.formData)
      exitExecutData(params).then(res => {
        const { result, code, msg } = res
        if (code === 0) {
          this.tablePage.total = result.total || 0
          this.tablePage.pageNum = result.pageNum || this.tablePage.pageNum
          this.tableData = result.list || []
        } else {
          this.$message.error(msg)
          this.tableData = []
          this.tablePage.total = 0
          this.tablePage.pageNum = 1
        }
        this.btnList[this.btnList.findIndex(item => item.label === '查询')].loading = false
        this.loading = false
        this.detailTableData = []
      })
    },
    // 查询二次封装
    searchList() {
      this.tablePage.pageNum = 1
      this.getList()
    },
    // 明细列表查询
    getDetailList() {
      this.detailLoading = true
      const params = Object.assign({
        refundOrderCode: this.selectData.refundOrderCode,
        queryType: 1,
        pickUpOrder: this.selectData.pickUpOrder
      })
      exitExecuteDetailData(params).then(res => {
        const { result, code, msg } = res
        if (code === 0) {
          this.oldDetailTableData = JSON.parse(JSON.stringify(result))
          this.detailTableData = result
        } else {
          this.$message.error(msg)
        }
        this.detailLoading = false
      })
    },
    // 页码切换时的方法
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.pageNum = currentPage
      this.tablePage.pageSize = pageSize
      this.getList()
    },
    // 打开容器编号的对话框
    openContainerNumber() {
      this.$refs.containerDialog.open()
    },
    // 关闭容器编号的对话框
    closeContainer(val) {
      this.detailSelsectData.containerCode = val.containerCode
    },
    // 购进退出单明细列表点击时触发的回调
    detailCurrentChangeEvent(val) {
      this.detailSelsectData = Object.assign(val.row)
    },
    // 判断选中行是否在数组中，并返回索引号
    getIndexIfObjectExists(array, target) {
      for (let i = 0; i < array.length; i++) {
        if (array[i].id === target.id) {
          return i;
        }
      }
      return -1;
    },
    // 打开退出货位的对话框
    openShelfLocationCode(row) {
      const detail = row
      this.$refs.dropOutDialog.open(detail)
    },
    // 关闭退出货位的对话框
    closeDropOut(filteredData) {
      // 将明细列表中选中的相同id的数据只保留一条
      let id = this.detailSelsectData.id
      let uniqueArray = this.detailTableData.filter(item => item.id !== id || (item.id === id && this.detailTableData.findIndex(subItem => subItem.id === id) === this.detailTableData.indexOf(item)))
      // 判断是否有数据
      if (filteredData.length) {
        // 找到索引号
        let indexCode = this.getIndexIfObjectExists(uniqueArray, this.detailSelsectData)
        if (indexCode !== -1) {
          // 删除原本的数据
          uniqueArray.splice(indexCode, 1)
          // 插入退出货位弹框传入的数据
          filteredData.forEach(item => {
            let newObject = { ...this.detailSelsectData, ...item }
            uniqueArray.splice(indexCode++, 0, newObject)
          })
          this.detailTableData = uniqueArray
        }
      } else {
        let targetIndex = uniqueArray.findIndex(item => item.id === this.detailSelsectData.id)
        // 拷贝一份初始数据作为更改的媒介
        let oldDetailTableDataCopy = JSON.parse(JSON.stringify(this.oldDetailTableData))
        if (targetIndex !== -1) {
          // 根据id从初始数据中找到数据
          let replacementObject = oldDetailTableDataCopy.find(item => item.id === this.detailSelsectData.id)
          // 将原本数据替换为找到的初始数据
          if (replacementObject) {
            // 如果初始的数据没有容器编号，则容器编号变为选中数据的
            if (!replacementObject.containerCode) {
              replacementObject.containerCode = this.detailSelsectData.containerCode
            }
            uniqueArray[targetIndex] = replacementObject
          }
          this.detailTableData = uniqueArray
        }
      }
    },
    // 提交请求
    submitApi() {
      let filteredArray = this.detailTableData.filter(item => item.refundCount > 0)
      filteredArray = filteredArray.map(item => ({ ...item, detailId: item.id }))
      const params = Object.assign({
        refundOrderCode: this.selectData.refundOrderCode,
        takeDeliveryAddress: this.takeDeliveryAddress
      }, {
        itemList: filteredArray
      })
      exitSubmitFinall(params).then(res => {
        const { code, msg, result } = res
        if (code === 0) {
          this.$message.success(msg || '提交成功')
          this.detailTableData = []
          this.selectData = {}
          this.searchList()
        } else {
          this.$message.error(msg || '提交失败')
        }
      })
    },
    // 提交
    submitFinall() {
      this.detailBtnList[this.detailBtnList.findIndex(item => item.label === '提交')].loading = true
      if (this.detailTableData.length) {
        let allEmpty = true
        for (let item of this.detailTableData) {
          if (item.shelfLocationCode !== '' && item.shelfLocationCode !== null) {
            allEmpty = false
            break
          }
        }
        if (allEmpty) {
          this.isAllred()
          this.detailBtnList[this.detailBtnList.findIndex(item => item.label === '提交')].loading = false
        } else {
          let allHaveCodes = true
          for (let item of this.detailTableData) {
            if (!item.containerCode) {
              allHaveCodes = false
              break
            }
          }
          if (allHaveCodes) {
            this.openAddress()
            this.detailBtnList[this.detailBtnList.findIndex(item => item.label === '提交')].loading = false
          } else {
            this.writeCode()
            this.detailBtnList[this.detailBtnList.findIndex(item => item.label === '提交')].loading = false
          }
        }
      } else {
        this.detailBtnList[this.detailBtnList.findIndex(item => item.label === '提交')].loading = false
        return
      }
    },
    // 整单冲红
    isAllred() {
      this.$confirm('是否整单冲红?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.submitApi()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    // 填写容器编号
    writeCode() {
      this.$alert(`请填写容器编号！`, '提示', {
        confirmButtonText: 'ok',
        callback: action => {
        }
      })
    },
    // 打开地址弹框
    openAddress() {
      this.$refs.addressDialog.open(this.selectData.supplierCode, this.detailTableData)
    },
    // 得到地址弹框传来的地址
    getAddress(addressMess) {
      this.takeDeliveryAddress = addressMess
    },
    // 关闭地址弹框
    closeAddress() {
      this.detailTableData = []
      this.searchList()
    }
  },
  // 页面挂载完毕执行的回调
  // mounted() {
  //   this.searchList()
  //   this.getOwners()
  // },
  activated() {
    this.$nextTick(() => {
      utils.pageActivated()
      this.getColumWidth('exitExecuteDetail', 'detailTableColums', 'detailTable')
      this.getColumWidth('exitExecute', 'tableColumns', 'xTable')
    })
    this.searchList()
    this.getOwners()
  },
}
</script>

<style lang="scss" scoped>
.table-box {
  height: 460px !important;
}
</style>