<template>
  <div class="drugRegulatory-codeBusiness">
    <xyy-panel title="查询条件">
      <!-- 按钮组 -->
      <btn-group slot="tools" :btn-list="btnList" />
      <!-- 查询条件 -->
      <el-form ref="formData" :model="formData" label-width="120px" class="clearfix">
        <el-row :gutter="20">
          <el-col :lg="12" :md="12">
            <el-form-item label="日期">
              <el-date-picker v-model="submissionTime" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="业主名称">
              <el-select v-model="formData.ownerCode" placeholder="请选择">
                <el-option label="全部" value=""></el-option>
                <el-option v-for="item in ownerCodeLists" :key="item.dictCode" :label="item.dictName"
                  :value="item.dictCode" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商品名称" prop="productCode">
              <!-- <el-input v-model="formData.productCode"></el-input> -->
              <el-input v-model="formData.productName" disabled>
                <el-button slot="append" icon="el-icon-delete" @click="deleteProductName" />
                <el-button slot="append" icon="el-icon-search" @click="openProductName" />
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :lg="6" :md="6">
            <el-form-item label="单位名称" prop="supplierName">
              <el-input v-model="formData.supplierName" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="码类型">
              <el-select v-model="formData.codeType" placeholder="请选择">
                <el-option label="全部" value=""></el-option>
                <el-option label="追溯码" value="1"></el-option>
                <el-option label="物流码" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="单据类型">
              <el-select v-model="formData.inStorageType" placeholder="请选择">
                <!-- <el-option label="全部" value=""></el-option> -->
                <el-option v-for="item in inStorageTypeLists" :key="item.dictCode" :label="item.dictName"
                  :value="item.dictCode" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="追溯码/物流码" prop="regulatoryCode">
              <el-input v-model="formData.regulatoryCode" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :lg="6" :md="6">
            <el-form-item label="生产厂家" prop="manufacturer">
              <el-input v-model="formData.manufacturer" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="单据编号" prop="orderCode">
              <el-input v-model="formData.orderCode" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </xyy-panel>
    <xyy-panel title="查询结果">
      <el-radio-group v-model="isExplort" size="medium" @change="seleteIndexChange">
        <el-radio-button label="0">未导出查询</el-radio-button>
        <el-radio-button label="1">已导出查询</el-radio-button>
      </el-radio-group>
      <div v-table class="table-box">
        <vxe-table ref="xTable" :loading="loading" highlight-current-row height="auto" :data="tableData" border="full">
          <vxe-table-column type="seq" title="序号" width="80" />
          <template v-for="item in tableColumns">
            <vxe-table-column v-if="item.visible" :key="item.field" :field="item.field" :title="item.title"
              :width="item.width" min-width="156px" max-width="220px">
            </vxe-table-column>
          </template>
        </vxe-table>
      </div>
      <!-- 分页器 -->
      <div class="pager">
        <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize" :total="tablePage.total"
          :layouts="[
            'PrevPage',
            'JumpNumber',
            'NextPage',
            'FullJump',
            'Sizes',
            'Total',
          ]" @page-change="handlePageChange" />
      </div>
    </xyy-panel>
    <productModal ref="productModal" @on-close="onCloseProductName"></productModal>
    <export-dialog ref="exportDialog" @exportInfo="exportInfo"></export-dialog>
  </div>
</template>
<script>
import utils from '@/utils';
import { exportData } from "@/api/public.js";
import productModal from "./components/productModal.vue";
import { tableColumns } from "./confiig";
import { getMinWidth } from "@/utils/index.js";
import { getDictionaries } from "@/api/businessQuery/inventory.js";
import { getCodeScanOrderList } from "@/api/businessQuery/goodsAllocation";
import exportDialog from "./components/exportDialog.vue";
import XEUtils from "xe-utils";
const end = new Date();
const start = XEUtils.getWhatDay(end, -6); // 获取最近 7 天的起始日期
const defaultBeginTime = XEUtils.toDateString(start, "yyyy-MM-dd"); // 将起始日期格式化为字符串
const defaultEndTime = XEUtils.toDateString(end, "yyyy-MM-dd"); // 将结束日期格式化为字符串
export default {
  name: "drugRegulatoryCodeBusiness",
  components: { productModal, exportDialog },
  data() {
    return {
      submissionTime: [defaultBeginTime, defaultEndTime], // 提交时间
      //时间限制
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      btnList: [
        {
          label: "查询",
          type: "primary",
          clickEvent: this.searchList,
          icon: "el-icon-search",
          code: "btn:wms:drugRegulatoryCodeBusiness:search",
        },
        {
          label: "重置",
          type: "info",
          clickEvent: this.reset,
          icon: "el-icon-setting",
          code: "btn:wms:drugRegulatoryCodeBusiness:reset",
        },
        {
          label: "导出",
          type: "success",
          clickEvent: this.export,
          icon: "el-icon-download",
          code: "btn:wms:drugRegulatoryCodeBusiness:export",
        },
      ],
      tablePage: {
        pageNum: 1,
        pageSize: 100,
        total: 0,
      },
      formData: {
        ownerCode: "XYYYXKJGS", // 业主名称
        productCode: "", // 商品编号
        productName: "", // 商品名称
        codeType: "", // 码类型
        inStorageType: 103, // 单据类型
        regulatoryCode: "", // 追溯码/物流码
        manufacturer: "", // 生产厂家
        orderCode: "", // 单据编号
        supplierName: '', // 供应商名称
      },
      ownerCodeLists: [], // 业务名称列表
      inStorageTypeLists: [
        {
          dictCode: 103,
          dictName: "采购入库",
        },
        {
          dictCode: 125,
          dictName: "采购验收",
        },
        {
          dictCode: 7,
          dictName: "销售退回",
        },
        {
          dictCode: 113,
          dictName: "购进退出",
        },
        {
          dictCode: 6,
          dictName: "出库作业",
        },
        {
          dictCode: 8,
          dictName: "不合格品报损",
        },
      ], // 单据类型列表
      loading: false,
      tableData: [],
      isExplort: "0", // 是否导出
      tableColumns: tableColumns(),
    };
  },
  methods: {
    // 获取业主列表
    getOwnerCodeLists() {
      const params = { dictType: "YZBM" };
      getDictionaries(params).then((res) => {
        const { code, msg, result } = res;
        if (code == 0) {
          this.ownerCodeLists = result || [];
        } else {
          this.$message.error(msg);
          this.ownerCodeLists = [];
        }
      });
    },
    // // 获取单据类型列表
    // getOrderTypeLists() {
    //   const params = { dictType: "DJLX" };
    //   getDictionaries(params).then((res) => {
    //     const { code, msg, result } = res;
    //     if (code == 0) {
    //       this.inStorageTypeLists = result || [];
    //     } else {
    //       this.$message.error(msg);
    //       this.inStorageTypeLists = [];
    //     }
    //   });
    // },
    // 查询首次
    // getList() {},
    // 查询二次封装
    searchList() {
      this.tablePage.pageNum = 1;
      this.getList();
    },
    // 切换是否导出按钮
    seleteIndexChange(val) {
      this.isExplort = val;
      this.searchList();
    },
    // 页码更改执行的回调
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.pageNum = currentPage;
      this.tablePage.pageSize = pageSize;
      this.getList();
    },
    deleteProductName() {
      this.formData.productCode = "";
      this.formData.productName = "";
    },
    //商品名称搜索
    openProductName() {
      //   console.log("商品名称");
      this.$refs.productModal.open();
    },
    //关闭商品名称查询
    onCloseProductName(row) {
      // console.log(222);
      this.formData.productName = row.productName;
      this.formData.productCode = row.productCode;
    },
    //分页查询追溯码信息列表
    async getList() {
      // 添加日期范围验证
      if (this.submissionTime && this.submissionTime[0] && this.submissionTime[1]) {
        const beginDate = new Date(this.submissionTime[0]);
        const endDate = new Date(this.submissionTime[1]);
        const diffTime = Math.abs(endDate - beginDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays > 6) {
          this.$message.warning('日期范围不能大于7天');
          return;
        }
      }

      this.loading = true;
      const params = {
        pageNo: this.tablePage.pageNum,
        pageSize: this.tablePage.pageSize,
        beginTime: this.submissionTime ? this.submissionTime[0] : "",
        endTime: this.submissionTime ? this.submissionTime[1] : "",
        whetherExport: this.isExplort,
        // codeType: this.formData.codeType,
        // inStorageType: this.formData.inStorageType,
        // ownerCode: this.formData.ownerCode,
        // productCode: this.formData.productCode,
        // productName: this.formData.productName,
        // supplierName: this.formData.supplierName,
        // regulatoryCode: this.formData.regulatoryCode,
        // manufacturer: this.formData.manufacturer,
        // orderCode: this.formData.orderCode,
        ...this.formData
      };
      // console.log(this.tablePage.pageSize, 'params');
      
      const res = await getCodeScanOrderList(params);
      if (res.code === 0) {
        this.tableData = res.result.list || [];
        this.loading = false;
        // this.tablePage = {
        //   pageNum: res.result.pageNum,
        //   total: res.result.total,
        // };
        this.tablePage.total = res.result.total;
        this.tablePage.pageNo = res.result.pageNum;
        this.tableColumns = getMinWidth(
          this.tableColumns,
          this.tableData,
          24,
          300,
          165
        );
      } else {
        this.message.error(res.msg);
        this.loading = false;
        this.tableData = [];
      }
    },
    reset() {
      this.$refs["formData"].resetFields();
      // this.formData.productCode = "";
      this.submissionTime = [defaultBeginTime, defaultEndTime] // 提交时间
      this.formData.ownerCode = "XYYYXKJGS";
      this.formData.productName = "";
      this.formData.orderCode = "";
      this.formData.manufacturer = "";
      this.formData.productCode = "";
      this.formData.regulatoryCode = "";
    },
    //导出
    exportPrint(taskBean, menuDesc) {
      const { pageNum, pageSize } = this.tablePage;
      const formInfo = {
        pageNo: this.tablePage.pageNum,
        pageSize: this.tablePage.pageSize,
        beginTime: this.submissionTime ? this.submissionTime[0] : "",
        endTime: this.submissionTime ? this.submissionTime[1] : "",
        whetherExport: this.isExplort,
        // codeType: this.formData.codeType,
        // inStorageType: this.formData.inStorageType,
        // ownerCode: this.formData.ownerCode,
        // productCode: this.formData.productCode,
        // productName: this.formData.productName,
        // supplierName: this.formData.supplierName,
        // regulatoryCode: this.formData.regulatoryCode,
        // manufacturer: this.formData.manufacturer,
        // orderCode: this.formData.orderCode,
        ...this.formData
      }
      const colNameDesc = this.tableColumns.map(item => item.title).join(',')
      const colName = this.tableColumns.map(item => item.field).join(',')
      const exportParams = JSON.stringify(formInfo)
      const params = {
        moduleName: 'WAREHOUSE',
        menuDesc: menuDesc,
        taskBean: taskBean,
        orgCode: this.storageTool.getUserInfo().warehouse.orgCode,
        warehouseCode: this.storageTool.getUserInfo().warehouse.warehouseCode,
        colNameDesc: colNameDesc,
        colName: colName,
        exportParams: exportParams
      }
      exportData(params).then(res => {
        const { code, msg, result } = res
        if (code === 0 && result) {
          this.$message({
            type: 'success',
            message: '导出成功，请前往下载中心查看！！!'
          })
        } else {
          this.$message.error(msg)
        }
      })
    },
    exportInfo(exportType) {
      if (exportType == 1) {
        this.exportPrint('com.xyy.wms.warehouse.api.remote.DrugRegulatoryCodeRecordExportRemote@exportCodeToXML', '追溯码导出xml')
      }
      if (exportType == 2) {
        this.exportPrint('com.xyy.wms.warehouse.api.remote.DrugRegulatoryCodeRecordExportRemote@exportCodeToCSV', '追溯码导出CSV')
      }
    },
    export() {
      if (!this.tableData.length) {
        this.$message.warning('无数据可供导出！')
        return false
      }
      this.$refs.exportDialog.open()
    },
  },
  activated() {
    this.$nextTick(()=>{
      utils.pageActivated()
    })
    // 获取字典信息
    this.getOwnerCodeLists();
    this.searchList();
    // this.getOrderTypeLists();
    // 动态渲染表格
    this.tableColumns = getMinWidth(
      this.tableColumns,
      this.tableData,
      24,
      300,
      165
    );
  },
};
</script>
