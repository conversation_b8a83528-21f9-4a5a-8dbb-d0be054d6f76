<template>
  <div class="mobile-chat-container">
    <!-- 顶部导航栏 -->
    <div class="chat-header">
      <div class="header-left">
        <i class="el-icon-arrow-left back-icon" @click="goBack"></i>
        <div class="store-info">
          <div class="store-name">藏龙岛鼎兴药店</div>
          <div class="store-status">待回复（自己）</div>
        </div>
      </div>
      <div class="header-right">
        <span class="end-chat-btn" @click="showEndChatDialog">结束会话</span>
      </div>
    </div>

    <!-- 欢迎访问标识 -->
    <div class="welcome-section">
      <span class="welcome-text">欢迎来访</span>
    </div>

    <!-- 聊天消息列表 -->
    <div class="chat-messages" ref="messagesContainer">
      <template v-for="(message, index) in messages" :key="index">
        <!-- 在第二条消息前显示接入会话分隔线 -->
        <div v-if="index === 1" class="session-divider">
          <span class="divider-text">接入会话</span>
        </div>

        <div
          class="message-item"
          :class="{ 'message-right': message.isUser, 'message-left': !message.isUser }"
        >
        <div class="message-avatar">
          <div
            class="avatar-circle"
            :style="{ backgroundColor: message.isUser ? '#22c55e' : '#22c55e' }"
          >
            {{ message.isUser ? '客' : '药' }}
          </div>
        </div>
        <div class="message-content">
          <div class="message-info">
            <span class="sender-name">{{ message.sender }}</span>
            <span class="message-time">{{ message.time }}</span>
          </div>
          <div class="message-bubble" :class="{ 'user-bubble': message.isUser, 'service-bubble': !message.isUser }">
            {{ message.content }}
          </div>
        </div>
      </template>
    </div>

    <!-- 客服超时结束会话提示 -->
    <div v-if="showTimeoutTip" class="timeout-tip">
      客服超时结束会话
    </div>

    <!-- 底部输入区域 -->
    <div class="chat-input-area">
      <div class="input-container">
        <input 
          v-model="inputMessage" 
          type="text" 
          placeholder="请输入" 
          class="message-input"
          @keyup.enter="sendMessage"
        />
        <button class="send-btn" @click="sendMessage">
          <i class="el-icon-plus"></i>
        </button>
      </div>
    </div>

    <!-- 发起对话按钮 -->
    <div v-if="showStartChatBtn" class="start-chat-container">
      <button class="start-chat-btn" @click="startChat">发起对话</button>
    </div>

    <!-- 结束对话确认弹窗 -->
    <el-dialog
      title=""
      :visible.sync="endChatDialogVisible"
      width="80%"
      :show-close="false"
      custom-class="end-chat-dialog"
      center
    >
      <div class="dialog-content">
        <div class="dialog-icon">
          <i class="el-icon-warning" style="color: #f56c6c; font-size: 24px;"></i>
        </div>
        <div class="dialog-title">确定结束对话?</div>
        <div class="dialog-subtitle">结束后可前往"历史会话"中查看</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="cancelEndChat">转至待跟进</el-button>
        <el-button type="primary" class="confirm-btn" @click="confirmEndChat">好的</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'MobileChatPage',
  data() {
    return {
      inputMessage: '',
      endChatDialogVisible: false,
      showTimeoutTip: false,
      showStartChatBtn: false,
      messages: [
        {
          id: 1,
          sender: '藏龙岛鼎兴药店',
          content: '您已确诊过此疾病并使用过该药，且无过敏史，无相关禁忌症和不良反应。请问您是否还有信息需要补充？如无，将依据病情为您开处方',
          time: '2024/10/11 16:01:52',
          isUser: false
        },
        {
          id: 2,
          sender: '某某客服',
          content: '您好，我是某某客服，很高兴为您服务',
          time: '2024/10/11 16:02:52',
          isUser: false
        },
        {
          id: 3,
          sender: '某某客服',
          content: '温馨提示：百亿补贴活动期间订单充值量较大，充值时效会受到影响，建议下单后2小时再查询到账情况；订单显示交易成功则代表购买成功',
          time: '2024/10/11 16:03:12',
          isUser: false
        },
        {
          id: 4,
          sender: '藏龙岛鼎兴药店',
          content: '我不想要了，现在申请退货可以吗？',
          time: '2024/10/11 16:40:13',
          isUser: true
        }
      ]
    }
  },
  mounted() {
    this.scrollToBottom()
    // 模拟客服超时
    setTimeout(() => {
      this.showTimeoutTip = true
      this.showStartChatBtn = true
    }, 5000)
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    sendMessage() {
      if (this.inputMessage.trim()) {
        const newMessage = {
          id: Date.now(),
          sender: '我',
          content: this.inputMessage,
          time: this.formatTime(new Date()),
          isUser: true
        }
        this.messages.push(newMessage)
        this.inputMessage = ''
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messagesContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },
    formatTime(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
    },
    showEndChatDialog() {
      this.endChatDialogVisible = true
    },
    cancelEndChat() {
      this.endChatDialogVisible = false
      // 转至待跟进逻辑
      this.$message.success('已转至待跟进')
    },
    confirmEndChat() {
      this.endChatDialogVisible = false
      // 结束对话逻辑
      this.$message.success('对话已结束')
      this.goBack()
    },
    startChat() {
      this.showStartChatBtn = false
      this.showTimeoutTip = false
      // 重新开始对话逻辑
      this.$message.success('对话已重新开始')
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;

  .header-left {
    display: flex;
    align-items: center;

    .back-icon {
      font-size: 20px;
      color: #333;
      margin-right: 12px;
      cursor: pointer;
    }

    .store-info {
      .store-name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        line-height: 1.2;
      }

      .store-status {
        font-size: 12px;
        color: #999;
        margin-top: 2px;
      }
    }
  }

  .header-right {
    .end-chat-btn {
      font-size: 14px;
      color: #666;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;

      &:hover {
        background-color: #f0f0f0;
      }
    }
  }
}

.welcome-section {
  text-align: center;
  padding: 16px;
  background-color: #f5f5f5;

  .welcome-text {
    font-size: 14px;
    color: #999;
    background-color: #e5e5e5;
    padding: 6px 12px;
    border-radius: 12px;
    display: inline-block;
  }
}

.session-divider {
  text-align: center;
  padding: 12px 0;
  margin: 8px 0;

  .divider-text {
    font-size: 12px;
    color: #999;
    background-color: #e5e5e5;
    padding: 4px 12px;
    border-radius: 10px;
    display: inline-block;
  }
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
  background-color: #f5f5f5;

  .message-item {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-start;

    &.message-right {
      flex-direction: row-reverse;

      .message-content {
        align-items: flex-end;
        margin-right: 8px;
      }

      .message-info {
        text-align: right;
      }
    }

    &.message-left {
      .message-content {
        margin-left: 8px;
      }
    }

    .message-avatar {
      .avatar-circle {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        font-weight: 500;
        flex-shrink: 0;
      }
    }

    .message-content {
      display: flex;
      flex-direction: column;
      max-width: 70%;

      .message-info {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        font-size: 12px;
        color: #999;

        .sender-name {
          margin-right: 8px;
        }
      }

      .message-bubble {
        padding: 10px 14px;
        border-radius: 12px;
        font-size: 14px;
        line-height: 1.4;
        word-wrap: break-word;

        &.service-bubble {
          background-color: #fff;
          color: #333;
          border: 1px solid #e5e5e5;
        }

        &.user-bubble {
          background-color: #95ec69;
          color: #333;
        }
      }
    }
  }
}

.timeout-tip {
  text-align: center;
  padding: 12px;
  font-size: 14px;
  color: #999;
  background-color: #f5f5f5;
}

.chat-input-area {
  padding: 12px 16px;
  background-color: #fff;
  border-top: 1px solid #e5e5e5;

  .input-container {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 20px;
    padding: 8px 12px;

    .message-input {
      flex: 1;
      border: none;
      background: transparent;
      outline: none;
      font-size: 14px;
      padding: 0;

      &::placeholder {
        color: #999;
      }
    }

    .send-btn {
      width: 28px;
      height: 28px;
      border: none;
      background-color: #4183d5;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-left: 8px;

      &:hover {
        background-color: #3a7bc8;
      }
    }
  }
}

.start-chat-container {
  padding: 16px;
  background-color: #fff;

  .start-chat-btn {
    width: 100%;
    padding: 12px;
    background-color: #22c55e;
    color: white;
    border: none;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;

    &:hover {
      background-color: #16a34a;
    }
  }
}

// 弹窗样式
::v-deep .end-chat-dialog {
  border-radius: 12px;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 24px 20px 16px;
  }

  .el-dialog__footer {
    padding: 0 20px 24px;
  }

  .dialog-content {
    text-align: center;

    .dialog-icon {
      margin-bottom: 16px;
    }

    .dialog-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;
    }

    .dialog-subtitle {
      font-size: 14px;
      color: #666;
    }
  }

  .dialog-footer {
    display: flex;
    gap: 12px;

    .el-button {
      flex: 1;
      border-radius: 20px;
      padding: 10px 20px;
      font-size: 14px;
    }

    .cancel-btn {
      background-color: #f5f5f5;
      border-color: #f5f5f5;
      color: #333;

      &:hover {
        background-color: #e5e5e5;
        border-color: #e5e5e5;
      }
    }

    .confirm-btn {
      background-color: #22c55e;
      border-color: #22c55e;

      &:hover {
        background-color: #16a34a;
        border-color: #16a34a;
      }
    }
  }
}
</style>
