import request from "@/utils/request";

//补货查询列表
export function getReplenishmentList(data) {
    return request({
        url: "/warehouse/pieceworkStatistics/getInquiryList",
        method: "post",
        data
    });
}

//损溢单明细查询
export function getLossAndOverList(data) {
    return request({
        url: "/warehouse/pieceworkStatistics/getList",
        method: "post",
        data
    });
}

//商品锁定单明细查询
export function getGoodsLockList(data) {
    return request({
        url: "/warehouse/pieceworkStatistics/findLockProductDetailList",
        method: "post",
        data
    });
}

//移库单查询
export function getMoveStoreList(data) {
    return request({
        url: "/warehouse/pieceworkStatistics/findWarehouseMovementAdDetailListCtr",
        method: "post",
        data
    })
}
//货位调整单查询
export function getGoodsPositionAdjustList(data) {
    return request({
        url: "/warehouse/pieceworkStatistics/findLocationMovementDetailListCtr",
        method: "post",
        data
    });
}